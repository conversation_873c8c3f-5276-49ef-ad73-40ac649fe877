package com.mall.project.task.executor;

import com.mall.project.service.areaAuthorize.AreaAuthorizeService;
import com.mall.project.service.cooperateEnterprise.CooperateEnterpriseService;
import com.mall.project.service.creditEvolve.CreditEvolveService;
import com.mall.project.service.functionDatas.FunctionDatasService;
import com.mall.project.service.mallBUsers.GetMallBUsersService;
import com.mall.project.service.quantifyCount.QuantifyCountService;
import com.mall.project.service.quantifyEvolve.QuantifyEvolveService;
import com.mall.project.service.quantizationValue.QuantizationValueService;
import com.mall.project.service.statusSet.StatusSetService;
import com.mall.project.service.sysDataStatistics.SysDataStatisticsService;
import com.mall.project.service.systemDatas.SystemDatasService;
import com.mall.project.service.systemInfo.SystemInfoService;
import com.mall.project.service.writeOffData.WriteOffDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 数据同步任务执行器
 * 负责执行各种数据同步相关的定时任务
 */
@Slf4j
@Component
public class DataSyncTaskExecutor {
    private static final DateTimeFormatter MONTH_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM");

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private GetMallBUsersService getMallBUsersService;

    @Autowired
    private CooperateEnterpriseService cooperateEnterpriseService;

    @Autowired
    private QuantifyCountService quantifyCountService;

    @Autowired
    private AreaAuthorizeService areaAuthorizeService;

    @Autowired
    private FunctionDatasService functionDatasService;

    @Autowired
    private SystemInfoService systemInfoService;

    @Autowired
    private SystemDatasService systemDatasService;

    @Autowired
    private QuantizationValueService quantizationValueService;

    @Autowired
    private CreditEvolveService creditEvolveService;

    @Autowired
    private WriteOffDataService writeOffDataService;

    @Autowired
    private SysDataStatisticsService sysDataStatisticsService;

    @Autowired
    private QuantifyEvolveService quantifyEvolveService;

    @Autowired
    private StatusSetService statusSetService;

    /**
     * 执行数据同步任务
     * 确保每一步都完成数据库操作后再执行下一步，任何异常都会回滚整个事务
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void executeDataSyncTasks() {
        log.info("开始执行数据同步任务");

        try {
            // 第1步：同步用户数据
            executeStepWithVerification("同步用户数据", () -> {
                getMallBUsersService.getMallBUsers();
            });

            // 第2步：读取超级管理员手机号码
            executeStepWithVerification("从mallB系统读取 超级管理员手机号码", () -> {
                getMallBUsersService.getSuperAdminPhone();
            });

            // 第3步：同步交易数据
            executeStepWithVerification("同步交易数据", () -> {
                cooperateEnterpriseService.getTradeDataFromMallB();
            });

            // 第4步：读取核销数据
            executeStepWithVerification("从mallB系统读取核销数据", () -> {
                writeOffDataService.getWriteOffDataFromMallB();
            });

            // 第5步：获取Admin量化值进化量数据
            executeStepWithVerification("从mallB系统获取 Admin量化值进化量数据", () -> {
                quantifyEvolveService.getAdminQuantifyEvolveFromMallB();
            });

            // 第6步：获取Admin量化进化量数据
            executeStepWithVerification("从mallB系统获取 Admin量化进化量数据", () -> {
                creditEvolveService.getAdminCreditEvolveFromMallB();
            });

            // 第7步：获取用户类型为B的量化值进化量数据
            executeStepWithVerification("从mallB系统获取用户类型为B的量化值进化量数据", () -> {
                quantifyEvolveService.getQuantifyEvolveFromMallB();
            });

            // 第8步：获取信用值进化量数据
            executeStepWithVerification("从mallB系统获取信用值进化量数据", () -> {
                creditEvolveService.getCreditEvolveFromMallB();
            });

            // 第9步：读取促销金数据
            executeStepWithVerification("从mallB系统读取 促销金数据", () -> {
                writeOffDataService.getPromotionDataFromMallB();
            });

            // 第10步：读取核销金数据
            executeStepWithVerification("从mallB系统读取 核销金数据", () -> {
                writeOffDataService.getWriteOffGoldFromMallB();
            });

            // 第11步：计算企业交易数据
            executeStepWithVerification("计算企业交易数据", () -> {
                cooperateEnterpriseService.updateEnterpriseTradeData();
            });

            // 第12步：计算量化数
            executeStepWithVerification("计算量化数计算", () -> {
                quantifyCountService.updateQuantifyCount();
            });

            // 第13步：计算C用户补贴金
            executeStepWithVerification("计算C用户补贴金", () -> {
                areaAuthorizeService.updateCSubsidy();
            });

            // 第14步：计算admin的量化数
            executeStepWithVerification("计算admin的量化数", () -> {
                quantifyCountService.adminDailyQuantity();
            });

            // 第15步：计算功能数据量化数
            executeStepWithVerification("计算功能数据量化数", () -> {
                functionDatasService.updateQuantifyCount();
            });

            // 第16步：计算量化进化量
            executeStepWithVerification("计算量化进化量", () -> {
                quantifyEvolveService.updateQuantifyEvolve();
            });

            // 第17步：计算信用值进化量
            executeStepWithVerification("计算信用值进化量", () -> {
                creditEvolveService.updateCreditEvolve();
            });

            // 第18步：计算用户状态为不正常的用户统计
            executeStepWithVerification("计算 用户状态为不正常的用户, 当日的量化值,补贴金统计", () -> {
                statusSetService.updateQuantifyAndSubsidy();
            });

            // 第19步：更新累计量化数
            executeStepWithVerification("更新累计量化数", () -> {
                quantifyCountService.updateTotalQuantifyCount();
            });

            // 第20步：计算企业交易数据统计
            executeStepWithVerification("计算企业交易数据", () -> {
                sysDataStatisticsService.statisticsData("query", "0", null);
            });

            // 第21步：计算企业量化数据统计
            executeStepWithVerification("计算企业量化数据", () -> {
                sysDataStatisticsService.statisticsData("query", "1", null);
            });

            // 第22步：计算量化率
            executeStepWithVerification("计算量化率", () -> {
                String currentMonth = LocalDate.now().format(MONTH_FORMAT);
                cooperateEnterpriseService.quantizationRate(currentMonth);
            });

            // 第23步：计算量化值
            executeStepWithVerification("计算量化值", () -> {
                quantizationValueService.updateQuantizationValue();
            });

            // 第24步：执行每日Admin量化值计算
            executeStepWithVerification("执行每日Admin量化值计算", () -> {
                quantizationValueService.adminDailyQuantifyValue("");
            });

            // 第25步：计算系统数据
            executeStepWithVerification("计算系统数据", () -> {
                systemDatasService.updateSystemDatas("");
            });

            // 第26步：计算系统信息
            executeStepWithVerification("计算系统信息", () -> {
                systemInfoService.updateSystemInfo();
            });

            // 第27步：删除过期授权信息
            executeStepWithVerification("删除授权表中过期的授权信息", () -> {
                areaAuthorizeService.deleteExpiredAuthorize();
            });

            log.info("数据同步任务执行完成");

        } catch (Exception e) {
            log.error("数据同步任务执行过程中发生错误，将回滚所有操作", e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 执行单个步骤并验证完成
     * 确保每个步骤的数据真正写入数据库后再继续下一步
     */
    private void executeStepWithVerification(String stepName, Runnable operation) {
        try {
            log.info("正在执行: {}", stepName);

            // 执行操作
            operation.run();

            // 由于使用JdbcTemplate，操作是同步的，数据会立即写入数据库
            // 添加一个简单的数据库连接验证，确保连接正常
            jdbcTemplate.queryForObject("SELECT 1", Integer.class);

            log.info("步骤完成: {} - 数据已写入数据库", stepName);

        } catch (Exception e) {
            log.error("步骤执行失败: {} - 错误: {}", stepName, e.getMessage(), e);
            throw new RuntimeException("数据同步步骤失败: " + stepName, e);
        }
    }
}
