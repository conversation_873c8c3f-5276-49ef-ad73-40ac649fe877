package com.mall.project.dao.statusSet;


import com.mall.project.dto.statusSet.StatusSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 状态设置数据访问对象
 */
@Repository
@Slf4j
public class StatusSetDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 保存或更新状态设置
     */
    public int saveOrUpdateStatusSet(StatusSet pojo, Integer updatePerson) {
        try {
            // 先查询是否存在该 type 的记录
            Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM status_set WHERE type = ?", Integer.class, pojo.getType());
            if (count > 0) {
                // 存在则更新
                String updateSql = "UPDATE status_set SET is_enabled = ?, update_person = ?, update_time = NOW() WHERE type = ?";
                return jdbcTemplate.update(updateSql, pojo.getIsEnabled(), updatePerson, pojo.getType());
            } else {
                // 不存在则插入
                String insertSql = "INSERT INTO status_set(is_enabled, type, update_person, update_time) VALUES (?, ?, ?, NOW())";
                return jdbcTemplate.update(insertSql, pojo.getIsEnabled(), pojo.getType(), updatePerson);
            }
        } catch (Exception e) {
            log.error("保存或更新状态设置失败: {}", e.getMessage());
            throw new RuntimeException("保存或更新状态设置失败: " + e.getMessage());
        }
    }

    /**
     * 查询状态设置
     */
    public Map<String, Object> getStatusSet(String type) {
        try{
            return jdbcTemplate.queryForMap("SELECT is_enabled, type FROM status_set WHERE type = ?", type);
        }catch (EmptyResultDataAccessException e){
            log.info("未找到状态设置信息，请设置状态设置");
            return null;
        }
    }

    /**
     * 量化值 统计
     */
    public String quantizationValue(){
        String sql = "select sum(c.quantify) as quantify from mall_b_users u,mall_b_users_count c\n" +
                "where u.phone = c.phone\n" +
                "and u.`status` <> 0\n" +
                "and c.quantify >0 " +
                "and (u.user_type = 'B' or u.user_type = 'CB') " +
                "and DATE(c.update_time) = CURDATE() ";
        return jdbcTemplate.queryForObject(sql, String.class);
    }

    /**
     * 量化值 输出
     */
    @Transactional
    public int outputQuantizationValue(String phone,String quantifyValue, int updatePerson){
        // 查看function_datas phone 是否已经存在,如果存在则更新,不存在则插入
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM function_datas WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, phone) == 0) {
            String sql = "INSERT INTO function_datas(enterprise_id,phone,quantify_value,update_person,update_date)VALUES (?,?,?,?,CURDATE() )";
            jdbcTemplate.update(sql, 1, phone, quantifyValue, updatePerson);
        }else{
            String sql = "UPDATE function_datas SET quantify_value = COALESCE(quantify_value, 0) + ?,update_person = ? WHERE phone = ? AND DATE(update_date) = CURDATE() ";
            jdbcTemplate.update(sql, quantifyValue, updatePerson, phone);
        }
        // 按时间顺序递减量化值：先减最早日期的，不够再减下一天的
        deductQuantifyByDateOrder(new BigDecimal(quantifyValue));
        // 把量化值记录到 quantization_value 表中, 对应的 phone
        if(jdbcTemplate.queryForObject("SELECT EXISTS(select 1 from quantization_value where phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, phone) == 0){
             String sql = "INSERT INTO quantization_value(phone,value,total_value,update_date)VALUES(?,?,?,CURDATE() )";
            jdbcTemplate.update(sql, phone, quantifyValue, new BigDecimal(quantifyValue).add(new BigDecimal(sumValue(phone))));
        }else{
             String sql = "UPDATE quantization_value SET value = value + ?,total_value = total_value + ? WHERE phone = ? AND DATE(update_date) = CURDATE() ";
            jdbcTemplate.update(sql, quantifyValue, quantifyValue, phone);
        }
        return 1;
    }

    /**
     * 统计 value ,phone 的和
     */
    public String sumValue(String phone) {
        try{
            String sql = "SELECT SUM(value) FROM quantization_value WHERE phone = ? AND DATE(update_date) < CURDATE() ";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 统计 platform_gold ,phone 的和
     */
    public String sumPlatformGold(String phone) {
        try{
            String sql = "SELECT SUM(platform_gold) FROM quantization_value WHERE phone = ? AND DATE(update_date) < CURDATE() ";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 流失的 补贴金 统计
     */
    public String lostSubsidy(){
        // 使用COALESCE或IFNULL函数处理NULL值，直接在SQL中返回0
        String sql = "SELECT COALESCE(SUM(value), 0) as subsidy_funds FROM csubsidy_result WHERE DATE(update_time) = CURDATE() ";
        return jdbcTemplate.queryForObject(sql, String.class);
    }

    /**
     * 补贴金 输出
     */
    @Transactional
    public int outputSubsidy(String phone,String subsidy, int updatePerson){
        // 查看function_datas phone 是否已经存在,如果存在则更新,不存在则插入
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM function_datas WHERE phone = ? AND DATE(update_date) = CURDATE())", Integer.class, phone) == 0) {
            String sql = "INSERT INTO function_datas(enterprise_id,phone,subsidy_funds,update_person,update_date)VALUES (?,?,?,?,CURDATE())";
            jdbcTemplate.update(sql, 1, phone, subsidy, updatePerson);
        }else{
            String sql = "UPDATE function_datas SET subsidy_funds = COALESCE(subsidy_funds, 0) + ?, update_person = ? WHERE phone = ? AND update_date = CURDATE() ";
            jdbcTemplate.update(sql, subsidy, updatePerson, phone);
        }
        // 按时间顺序递减补贴金：先减最早日期的，不够再减下一天的
        deductSubsidyByDateOrder(new BigDecimal(subsidy));
        // 把补贴金记录到 quantization_value 表中, 对应的 phone
        if(jdbcTemplate.queryForObject("SELECT EXISTS(select 1 from quantization_value where phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, phone) == 0){
            String sql = "INSERT INTO quantization_value(phone,platform_gold,total_platform_gold,update_date)VALUES(?,?,?,CURDATE() )";
            jdbcTemplate.update(sql, phone, subsidy, new BigDecimal(subsidy).add(new BigDecimal(sumPlatformGold(phone))));
        }else{
            String sql = "UPDATE quantization_value SET platform_gold = platform_gold + ?,total_platform_gold = total_platform_gold + ? WHERE phone = ? AND DATE(update_date) = CURDATE() ";
            jdbcTemplate.update(sql, subsidy, subsidy, phone);
        }
        return 1;
    }

    /**
     * 更新量化值和补贴金
     */
    public void updateQuantifyAndSubsidy(String quantifyValue, String subsidy) {
        // 插入数据到quantify_subsidy_total 表,如果update_date 已经存在则更新,不存在则插入,使用SELECT EXISTS 判断
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM quantify_subsidy_total WHERE DATE(update_date) = CURDATE() )", Integer.class) == 0) {
            String sql = "INSERT INTO quantify_subsidy_total(quantify,subsidy,update_date)VALUES(?, ?, CURDATE() )";
            jdbcTemplate.update(sql, quantifyValue, subsidy);
        }else{
            String sql = "UPDATE quantify_subsidy_total SET quantify = ?,subsidy = ? WHERE DATE(update_date) = CURDATE() ";
            jdbcTemplate.update(sql, quantifyValue, subsidy);
        }
    }

    /**
     * 查询量化值和补贴金
     */
    public Map<String, Object> getQuantifyAndSubsidy() {
        try {
            String sql = "SELECT sum(quantify) as quantify,sum(subsidy) as subsidy FROM quantify_subsidy_total WHERE DATE(update_date) <= CURDATE()";
            return jdbcTemplate.queryForMap(sql);
        } catch (EmptyResultDataAccessException e) {
            //log.info("未找到昨天的量化值和补贴金数据");
            return null;
        }
    }

    /**
     * 按时间顺序递减量化值：先减最早日期的，不够再减下一天的
     * @param quantifyToDeduct 需要减去的量化值
     */
    private void deductQuantifyByDateOrder(BigDecimal quantifyToDeduct) {
        // 查询所有有量化值余额的记录，按日期升序排列（最早的在前）
        String selectSql = "SELECT id, quantify, update_date FROM quantify_subsidy_total " +
                          "WHERE quantify > 0 " +
                          "ORDER BY update_date ASC";

        List<Map<String, Object>> quantifyRecords = jdbcTemplate.queryForList(selectSql);

        BigDecimal remainingToDeduct = quantifyToDeduct;

        for (Map<String, Object> record : quantifyRecords) {
            if (remainingToDeduct.compareTo(BigDecimal.ZERO) <= 0) {
                break; // 已经减完了
            }

            Long recordId = ((Number) record.get("id")).longValue();
            BigDecimal currentQuantify = new BigDecimal(record.get("quantify").toString());

            if (currentQuantify.compareTo(remainingToDeduct) >= 0) {
                // 当前记录的量化值足够减去剩余金额
                String updateSql = "UPDATE quantify_subsidy_total SET quantify = quantify - ? WHERE id = ?";
                jdbcTemplate.update(updateSql, remainingToDeduct, recordId);
                remainingToDeduct = BigDecimal.ZERO;
            } else {
                // 当前记录的量化值不够，全部减完，继续下一条记录
                String updateSql = "UPDATE quantify_subsidy_total SET quantify = 0 WHERE id = ?";
                jdbcTemplate.update(updateSql, recordId);
                remainingToDeduct = remainingToDeduct.subtract(currentQuantify);
            }
        }

        // 如果还有剩余未减完的金额，说明总量化值不足，可以记录日志或抛出异常
        if (remainingToDeduct.compareTo(BigDecimal.ZERO) > 0) {
            log.warn("量化值不足，还有 {} 未能减去", remainingToDeduct);
        }
    }

    /**
     * 按时间顺序递减补贴金：先减最早日期的，不够再减下一天的
     * @param subsidyToDeduct 需要减去的补贴金额
     */
    private void deductSubsidyByDateOrder(BigDecimal subsidyToDeduct) {
        // 查询所有有补贴余额的记录，按日期升序排列（最早的在前）
        String selectSql = "SELECT id, subsidy, update_date FROM quantify_subsidy_total " +
                          "WHERE subsidy > 0 " +
                          "ORDER BY update_date ASC";

        List<Map<String, Object>> subsidyRecords = jdbcTemplate.queryForList(selectSql);

        BigDecimal remainingToDeduct = subsidyToDeduct;

        for (Map<String, Object> record : subsidyRecords) {
            if (remainingToDeduct.compareTo(BigDecimal.ZERO) <= 0) {
                break; // 已经减完了
            }

            Long recordId = ((Number) record.get("id")).longValue();
            BigDecimal currentSubsidy = new BigDecimal(record.get("subsidy").toString());

            if (currentSubsidy.compareTo(remainingToDeduct) >= 0) {
                // 当前记录的补贴足够减去剩余金额
                String updateSql = "UPDATE quantify_subsidy_total SET subsidy = subsidy - ? WHERE id = ?";
                jdbcTemplate.update(updateSql, remainingToDeduct, recordId);
                remainingToDeduct = BigDecimal.ZERO;
            } else {
                // 当前记录的补贴不够，全部减完，继续下一条记录
                String updateSql = "UPDATE quantify_subsidy_total SET subsidy = 0 WHERE id = ?";
                jdbcTemplate.update(updateSql, recordId);
                remainingToDeduct = remainingToDeduct.subtract(currentSubsidy);
            }
        }

        // 如果还有剩余未减完的金额，说明总补贴不足，可以记录日志或抛出异常
        if (remainingToDeduct.compareTo(BigDecimal.ZERO) > 0) {
            log.warn("补贴金不足，还有 {} 未能减去", remainingToDeduct);
        }
    }

    /**
     * 读取企业
     */
    public List<Map<String, Object>> queryEnterprise() {
        String sql = "select id,name,table_name from cooperate_enterprise";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 查询企业是否存在手机号
     */
    public boolean checkPhoneExists(String phone,String tableName) {
        String sql = "SELECT EXISTS(SELECT 1 FROM " + tableName + " WHERE phone = ? and status = 0)";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, phone);
        return count != null && count > 0;
    }
}
